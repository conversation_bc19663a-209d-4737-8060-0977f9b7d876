#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Telegram Userbot - Auto Reply When Inactive
===========================================
Personal Telegram userbot that automatically replies to private messages
only when the user is inactive for a specified period.
"""

import asyncio
import json
import logging
import os
import signal
import sys
from datetime import datetime, timedelta
from typing import Dict, Set, Optional

try:
    from telethon import TelegramClient, events
    from telethon.tl.types import User, PeerUser
except ImportError:
    print("❌ Error: Telethon library not found!")
    print("📦 Please install it with: pip install telethon")
    sys.exit(1)


class TelegramUserbot:
    def __init__(self, config_path: str = "config.json"):
        """Initialize the userbot with configuration."""
        # Initialize basic attributes first
        self.logger = None
        self.client = None
        # Set last activity to past time so bot starts as "inactive"
        self.last_activity_time = datetime.now() - timedelta(hours=1)
        self.replied_users: Dict[int, datetime] = {}
        self.running = False

        # Load config
        self.config = self.load_config(config_path)

        # Setup logging after config is loaded
        self.setup_logging()
        
    def load_config(self, config_path: str) -> dict:
        """Load configuration from JSON file."""
        try:
            if not os.path.exists(config_path):
                print(f"Error: Configuration file {config_path} not found!")
                sys.exit(1)

            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # Validate required fields
            required_fields = ['api_id', 'api_hash', 'phone_number']
            for field in required_fields:
                if not config.get(field) or config[field] == f"YOUR_{field.upper()}":
                    print(f"\n❌ Error: Please configure {field} in {config_path}")
                    print(f"📝 You need to get your API credentials from https://my.telegram.org")
                    print(f"🔧 Edit {config_path} and replace YOUR_{field.upper()} with your actual {field}")
                    sys.exit(1)

            return config

        except json.JSONDecodeError as e:
            print(f"Error: Invalid JSON in config file: {e}")
            sys.exit(1)
        except Exception as e:
            print(f"Error loading config: {e}")
            sys.exit(1)
            
    def setup_logging(self):
        """Setup logging configuration."""
        log_level = logging.DEBUG if self.config.get('debug_mode', False) else logging.INFO
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('userbot.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger('TelegramUserbot')
        
        if not self.config.get('enable_logging', True):
            self.logger.setLevel(logging.CRITICAL)
            
    async def initialize_client(self):
        """Initialize and connect the Telegram client."""
        try:
            self.client = TelegramClient(
                self.config['session_name'],
                self.config['api_id'],
                self.config['api_hash']
            )
            
            await self.client.start(phone=self.config['phone_number'])
            
            # Verify we're connected
            me = await self.client.get_me()
            self.logger.info(f"Successfully connected as {me.first_name} (@{me.username})")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize client: {e}")
            return False
            
    def is_user_inactive(self) -> bool:
        """Check if user has been inactive for the configured period."""
        inactivity_threshold = timedelta(minutes=self.config.get('inactivity_minutes', 5))
        return datetime.now() - self.last_activity_time > inactivity_threshold
        
    def should_auto_reply(self, user_id: int) -> bool:
        """Determine if we should send an auto-reply to this user."""
        # Check if user is in excluded list
        if user_id in self.config.get('excluded_users', []):
            if self.logger:
                self.logger.debug(f"User {user_id} is in excluded list")
            return False

        # Check if we've already replied to this user recently
        if user_id in self.replied_users:
            rate_limit = timedelta(seconds=self.config.get('rate_limit_seconds', 60))
            if datetime.now() - self.replied_users[user_id] < rate_limit:
                if self.logger:
                    self.logger.debug(f"Rate limit active for user {user_id}")
                return False

        # Check if user is inactive
        is_inactive = self.is_user_inactive()
        if self.logger:
            inactive_minutes = (datetime.now() - self.last_activity_time).total_seconds() / 60
            self.logger.info(f"User inactive for {inactive_minutes:.1f} minutes. Should reply: {is_inactive}")
        return is_inactive
        
    async def send_auto_reply(self, event):
        """Send auto-reply message to the user."""
        try:
            user_id = event.sender_id
            auto_reply_message = self.config.get('auto_reply_message', 
                                               'مرحباً! أنا غير متاح حالياً، سأرد عليك في أقرب وقت ممكن. 🙏')
            
            await event.respond(auto_reply_message)
            
            # Record that we replied to this user
            self.replied_users[user_id] = datetime.now()
            
            # Get user info for logging
            sender = await event.get_sender()
            user_name = getattr(sender, 'first_name', 'Unknown')
            
            self.logger.info(f"Auto-replied to {user_name} (ID: {user_id})")
            
        except Exception as e:
            self.logger.error(f"Error sending auto-reply: {e}")
            
    def update_last_activity(self):
        """Update the last activity timestamp."""
        self.last_activity_time = datetime.now()
        
        # Clear replied users when user becomes active
        if self.replied_users:
            self.replied_users.clear()
            if self.logger:
                self.logger.debug("Cleared replied users list - user is now active")
            
    def setup_event_handlers(self):
        """Setup event handlers for incoming and outgoing messages."""
        
        @self.client.on(events.NewMessage(outgoing=True))
        async def handle_outgoing_message(event):
            """Handle outgoing messages to track user activity."""
            self.update_last_activity()
            if self.logger:
                self.logger.debug("User activity detected - updated last activity time")
            
        @self.client.on(events.NewMessage(incoming=True))
        async def handle_incoming_message(event):
            """Handle incoming messages for auto-reply."""
            try:
                # Only handle private messages
                if not isinstance(event.message.peer_id, PeerUser):
                    if self.logger:
                        self.logger.debug("Ignoring non-private message")
                    return

                # Skip if sender is a bot
                sender = await event.get_sender()
                if isinstance(sender, User) and sender.bot:
                    if self.logger:
                        self.logger.debug("Ignoring message from bot")
                    return

                user_id = event.sender_id
                sender_name = getattr(sender, 'first_name', 'Unknown')

                if self.logger:
                    self.logger.info(f"Received private message from {sender_name} (ID: {user_id})")

                # Check if we should auto-reply
                if self.should_auto_reply(user_id):
                    await self.send_auto_reply(event)
                else:
                    if self.logger:
                        self.logger.info(f"Not replying to {sender_name} - conditions not met")
                    
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Error handling incoming message: {e}")
                else:
                    print(f"Error handling incoming message: {e}")
                
    async def run(self):
        """Main run loop for the userbot."""
        if self.logger:
            self.logger.info("Starting Telegram Userbot...")
        else:
            print("Starting Telegram Userbot...")
        
        # Initialize client
        if not await self.initialize_client():
            return
            
        # Setup event handlers
        self.setup_event_handlers()
        
        self.running = True
        self.logger.info("Userbot is now running and monitoring messages...")
        
        try:
            # Keep the client running
            await self.client.run_until_disconnected()
        except KeyboardInterrupt:
            self.logger.info("Received interrupt signal, shutting down...")
        except Exception as e:
            self.logger.error(f"Unexpected error: {e}")
        finally:
            await self.shutdown()
            
    async def shutdown(self):
        """Gracefully shutdown the userbot."""
        self.running = False
        self.logger.info("Shutting down userbot...")
        
        if self.client:
            await self.client.disconnect()
            
        self.logger.info("Userbot stopped successfully")


def signal_handler(signum, frame):
    """Handle system signals for graceful shutdown."""
    print("\nReceived shutdown signal, stopping userbot...")
    sys.exit(0)


async def main():
    """Main function to run the userbot."""
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # Create and run userbot
        userbot = TelegramUserbot()
        await userbot.run()
    except Exception as e:
        print(f"Error in main: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nUserbot stopped by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
