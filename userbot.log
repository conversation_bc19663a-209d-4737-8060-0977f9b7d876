2025-07-11 20:00:25,779 - Telegram<PERSON><PERSON>bot - INFO - Starting Telegram Userbot...
2025-07-11 20:00:25,798 - telethon.network.mtprotosender - INFO - Connecting to 149.154.167.51:443/TcpFull...
2025-07-11 20:00:28,555 - telethon.network.mtprotosender - INFO - Connection to 149.154.167.51:443/TcpFull complete!
2025-07-11 20:11:43,920 - TelegramUserbot - INFO - Starting Telegram Userbot...
2025-07-11 20:11:43,922 - telethon.network.mtprotosender - INFO - Connecting to 149.154.167.51:443/TcpFull...
2025-07-11 20:11:44,176 - telethon.network.mtprotosender - INFO - Connection to 149.154.167.51:443/TcpFull complete!
2025-07-11 20:11:45,486 - telethon.client.users - WARNING - Telegram is having internal issues AuthRestartError: Restart the authorization process (caused by SendCodeRequest)
2025-07-11 20:12:09,732 - TelegramUserbot - INFO - Successfully connected as كرار حيدر جواد عبد/م3 (@BBDKB)
2025-07-11 20:12:09,733 - TelegramUserbot - INFO - Userbot is now running and monitoring messages...
2025-07-11 20:13:42,288 - TelegramUserbot - INFO - Starting Telegram Userbot...
2025-07-11 20:13:42,290 - telethon.network.mtprotosender - INFO - Connecting to 149.154.167.51:443/TcpFull...
2025-07-11 20:13:42,406 - telethon.network.mtprotosender - INFO - Connection to 149.154.167.51:443/TcpFull complete!
2025-07-11 20:13:46,551 - TelegramUserbot - INFO - Successfully connected as كرار حيدر جواد عبد/م3 (@BBDKB)
2025-07-11 20:13:46,561 - TelegramUserbot - INFO - Userbot is now running and monitoring messages...
2025-07-11 20:15:32,664 - TelegramUserbot - INFO - Starting Telegram Userbot...
2025-07-11 20:15:32,667 - telethon.network.mtprotosender - INFO - Connecting to 149.154.167.51:443/TcpFull...
2025-07-11 20:15:32,667 - telethon.network.mtprotosender - DEBUG - Connection attempt 1...
2025-07-11 20:15:32,947 - telethon.network.mtprotosender - DEBUG - Connection success!
2025-07-11 20:15:32,947 - telethon.network.mtprotosender - DEBUG - Starting send loop
2025-07-11 20:15:32,950 - telethon.network.mtprotosender - DEBUG - Starting receive loop
2025-07-11 20:15:32,950 - telethon.network.mtprotosender - INFO - Connection to 149.154.167.51:443/TcpFull complete!
2025-07-11 20:15:32,955 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:32,956 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874195045360480 to InvokeWithLayerRequest (1a6b05fd160)
2025-07-11 20:15:32,956 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 76 bytes for sending
2025-07-11 20:15:32,958 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:32,958 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:32,959 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:33,118 - telethon.network.mtprotosender - DEBUG - Handling bad salt for message 7525874195045360480
2025-07-11 20:15:33,118 - telethon.network.mtprotosender - DEBUG - 1 message(s) will be resent
2025-07-11 20:15:33,119 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:33,119 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874195994993720 to InvokeWithLayerRequest (1a6b05fd160)
2025-07-11 20:15:33,120 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 76 bytes for sending
2025-07-11 20:15:33,121 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:33,122 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:33,122 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874196006203208 to MsgsAck (1a6b05fd550)
2025-07-11 20:15:33,123 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:15:33,124 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:33,124 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:33,343 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:15:33,343 - telethon.network.mtprotosender - DEBUG - Handling new session created
2025-07-11 20:15:33,344 - telethon.network.mtprotosender - DEBUG - Handling acknowledge for [7525874195994993720]
2025-07-11 20:15:33,344 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:33,396 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874195994993720
2025-07-11 20:15:33,398 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:33,399 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874197114327944 to GetUsersRequest (1a6b05fd940)
2025-07-11 20:15:33,400 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-07-11 20:15:33,401 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:33,401 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:33,402 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874197124003920 to MsgsAck (1a6b0604410)
2025-07-11 20:15:33,402 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 60 bytes for sending
2025-07-11 20:15:33,403 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:33,404 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:33,601 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874197114327944
2025-07-11 20:15:33,602 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:33,605 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874197937686476 to GetStateRequest (1a6b05fd940)
2025-07-11 20:15:33,605 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 20 bytes for sending
2025-07-11 20:15:33,607 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:33,607 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:33,608 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874197947948968 to MsgsAck (1a6b060c180)
2025-07-11 20:15:33,608 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:15:33,609 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:33,610 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:34,085 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:15:34,085 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874197937686476
2025-07-11 20:15:34,086 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:15:34,087 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:34,088 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874200163068928 to GetDifferenceRequest (1a6b05fd940)
2025-07-11 20:15:34,088 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:15:34,090 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:34,090 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:34,091 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874200175265468 to MsgsAck (1a6b060c2b0)
2025-07-11 20:15:34,091 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 52 bytes for sending
2025-07-11 20:15:34,093 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:34,094 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:34,483 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874200163068928
2025-07-11 20:15:34,484 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:34,486 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874201754836240 to GetUsersRequest (1a6b0604a50)
2025-07-11 20:15:34,486 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-07-11 20:15:34,489 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:34,489 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:34,490 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874201773372808 to MsgsAck (1a6af252570)
2025-07-11 20:15:34,492 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:15:34,494 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:34,495 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:34,728 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874201754836240
2025-07-11 20:15:34,734 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:34,736 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874202754861032 to GetUsersRequest (1a6b0604a50)
2025-07-11 20:15:34,737 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 32 bytes for sending
2025-07-11 20:15:34,741 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:34,741 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:34,742 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874202782357372 to MsgsAck (1a6af02b680)
2025-07-11 20:15:34,745 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:15:34,752 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:34,755 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:35,203 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874202754861032
2025-07-11 20:15:35,204 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:35,205 - TelegramUserbot - INFO - Successfully connected as كرار حيدر جواد عبد/م3 (@BBDKB)
2025-07-11 20:15:35,206 - TelegramUserbot - INFO - Userbot is now running and monitoring messages...
2025-07-11 20:15:35,208 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874204937394896 to GetStateRequest (1a6b0604550)
2025-07-11 20:15:35,208 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 20 bytes for sending
2025-07-11 20:15:35,212 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:35,216 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:35,217 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874204975690644 to MsgsAck (1a6b05c8270)
2025-07-11 20:15:35,218 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:15:35,223 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:35,223 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:35,410 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:15:35,411 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:35,419 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874205783237256 to GetMessagesRequest (1a6b05fdd30)
2025-07-11 20:15:35,420 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:15:35,423 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:35,424 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:35,425 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874205805745880 to MsgsAck (1a6af1bba50)
2025-07-11 20:15:35,425 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:15:35,431 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:35,432 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:35,551 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874204937394896
2025-07-11 20:15:35,552 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:35,688 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874205783237256
2025-07-11 20:15:35,690 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:35,692 - TelegramUserbot - INFO - Received private message from main (ID: 7859654594)
2025-07-11 20:15:35,693 - TelegramUserbot - INFO - User inactive for 60.1 minutes. Should reply: True
2025-07-11 20:15:35,705 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874206927299300 to SendMessageRequest (1a6b05fde80)
2025-07-11 20:15:35,707 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 168 bytes for sending
2025-07-11 20:15:35,714 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:35,716 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:35,717 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874206976179876 to MsgsAck (1a6b05c1b50)
2025-07-11 20:15:35,719 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 44 bytes for sending
2025-07-11 20:15:35,723 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:15:35,724 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:15:36,156 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874206927299300
2025-07-11 20:15:36,157 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:36,159 - TelegramUserbot - INFO - Auto-replied to main (ID: 7859654594)
2025-07-11 20:15:36,498 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:15:36,500 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:38,537 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:15:38,538 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:38,549 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:15:38,549 - telethon.network.mtprotosender - DEBUG - Handling gzipped data
2025-07-11 20:15:38,550 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:15:38,550 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:15:38,551 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:38,552 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:15:38,552 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:15:42,855 - telethon.network.mtprotosender - DEBUG - Handling gzipped data
2025-07-11 20:15:42,858 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:15:42,859 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:45,825 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:15:45,829 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:15:51,328 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:15:51,329 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:16:01,295 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:16:01,295 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:16:01,295 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:16:01,296 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:16:01,296 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:16:02,533 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:16:02,533 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShort
2025-07-11 20:16:02,534 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:16:02,534 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:16:02,535 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:16:03,278 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:16:03,279 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:16:03,286 - telethon.network.mtprotosender - DEBUG - Handling gzipped data
2025-07-11 20:16:03,286 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:16:03,287 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:16:03,287 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:16:03,288 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:16:04,452 - telethon.network.mtprotosender - DEBUG - Handling update UpdateShortMessage
2025-07-11 20:16:04,453 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:16:04,454 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874330477094636 to GetMessagesRequest (1a6b0605e50)
2025-07-11 20:16:04,456 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:16:04,457 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:16:04,458 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:16:04,458 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874330493280396 to MsgsAck (1a6b05f96d0)
2025-07-11 20:16:04,459 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 172 bytes for sending
2025-07-11 20:16:04,461 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:16:04,461 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:16:04,743 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874330477094636
2025-07-11 20:16:04,744 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:16:04,744 - TelegramUserbot - INFO - Received private message from ™ StarBoy™ (ID: 6387578933)
2025-07-11 20:16:04,745 - TelegramUserbot - INFO - User inactive for 60.5 minutes. Should reply: True
2025-07-11 20:16:04,745 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874331643219932 to SendMessageRequest (1a6b0605d10)
2025-07-11 20:16:04,746 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 168 bytes for sending
2025-07-11 20:16:04,748 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:16:04,749 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:16:04,749 - telethon.extensions.messagepacker - DEBUG - Assigned msg_id = 7525874331657478316 to MsgsAck (1a6b05f9c70)
2025-07-11 20:16:04,750 - telethon.network.mtprotosender - DEBUG - Encrypting 1 message(s) in 36 bytes for sending
2025-07-11 20:16:04,751 - telethon.network.mtprotosender - DEBUG - Encrypted messages put in a queue to be sent
2025-07-11 20:16:04,751 - telethon.network.mtprotosender - DEBUG - Waiting for messages to send...
2025-07-11 20:16:05,092 - telethon.network.mtprotosender - DEBUG - Handling container
2025-07-11 20:16:05,092 - telethon.network.mtprotosender - DEBUG - Handling RPC result for message 7525874331643219932
2025-07-11 20:16:05,093 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:16:05,093 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
2025-07-11 20:16:05,094 - TelegramUserbot - INFO - Auto-replied to ™ StarBoy™ (ID: 6387578933)
2025-07-11 20:16:05,094 - TelegramUserbot - DEBUG - Ignoring non-private message
2025-07-11 20:16:05,272 - telethon.network.mtprotosender - DEBUG - Handling update Updates
2025-07-11 20:16:05,272 - telethon.network.mtprotosender - DEBUG - Receiving items from the network...
